import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'hospital_records.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            hospital TEXT,
            type TEXT,
            amount REAL,
            remarks TEXT
          )
        ''');
      },
    );
  }

  Future<int> insertRecord(Map<String, dynamic> record) async {
    final db = await instance.database;
    return await db.insert('records', record);
  }

  Future<List<Map<String, dynamic>>> fetchRecords() async {
    final db = await instance.database;
    return await db.query('records', orderBy: 'id DESC');
  }

  // Advanced analytics methods
  Future<List<Map<String, dynamic>>> getRecordsByDateRange(
      String startDate, String endDate) async {
    final db = await instance.database;
    return await db.query(
      'records',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [startDate, endDate],
      orderBy: 'date DESC',
    );
  }

  Future<List<Map<String, dynamic>>> getMonthlyTotals() async {
    final db = await instance.database;
    return await db.rawQuery('''
      SELECT
        substr(date, 1, 7) as month,
        SUM(amount) as total_amount,
        COUNT(*) as record_count
      FROM records
      GROUP BY substr(date, 1, 7)
      ORDER BY month DESC
    ''');
  }

  Future<List<Map<String, dynamic>>> getHospitalWiseTotals() async {
    final db = await instance.database;
    return await db.rawQuery('''
      SELECT
        hospital,
        SUM(amount) as total_amount,
        COUNT(*) as record_count,
        AVG(amount) as avg_amount
      FROM records
      GROUP BY hospital
      ORDER BY total_amount DESC
    ''');
  }

  Future<List<Map<String, dynamic>>> getTypeWiseTotals() async {
    final db = await instance.database;
    return await db.rawQuery('''
      SELECT
        type,
        SUM(amount) as total_amount,
        COUNT(*) as record_count,
        AVG(amount) as avg_amount
      FROM records
      WHERE type IS NOT NULL AND type != ''
      GROUP BY type
      ORDER BY total_amount DESC
    ''');
  }

  Future<Map<String, dynamic>> getOverallStats() async {
    final db = await instance.database;
    final result = await db.rawQuery('''
      SELECT
        COUNT(*) as total_records,
        SUM(amount) as total_amount,
        AVG(amount) as avg_amount,
        MIN(amount) as min_amount,
        MAX(amount) as max_amount,
        MIN(date) as earliest_date,
        MAX(date) as latest_date
      FROM records
    ''');
    return result.first;
  }

  Future<List<Map<String, dynamic>>> getRecentRecords(int limit) async {
    final db = await instance.database;
    return await db.query(
      'records',
      orderBy: 'id DESC',
      limit: limit,
    );
  }

  Future<List<Map<String, dynamic>>> getDailyTotals(int days) async {
    final db = await instance.database;
    return await db.rawQuery('''
      SELECT
        date,
        SUM(amount) as total_amount,
        COUNT(*) as record_count
      FROM records
      WHERE date >= date('now', '-$days days')
      GROUP BY date
      ORDER BY date DESC
    ''');
  }
}
