import 'package:flutter/material.dart';
import 'package:swarnadiary/reports.dart';
import 'package:swarnadiary/reusableButton.dart';
import 'package:swarnadiary/reusableTextfield.dart';
import 'package:swarnadiary/DatabaseHelper.dart';


class DataEntryScreen extends StatefulWidget {
  @override
  _DataEntryScreenState createState() => _DataEntryScreenState();
}

class _DataEntryScreenState extends State<DataEntryScreen> {
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _type = TextEditingController();
  final TextEditingController _amount = TextEditingController();
  final TextEditingController _remarks = TextEditingController();
  String? _selectedHospital;

  Future<void> _selectDate(BuildContext context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      setState(() {
        _dateController.text = "${pickedDate.toLocal()}".split(' ')[0];
      });
    }
  }

  Future<void> _saveData() async {
    if (_dateController.text.isEmpty || _selectedHospital == null || _amount.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text("Please fill all required fields."),
        backgroundColor: Colors.red,
      ));
      return;
    }

    final record = {
      'date': _dateController.text,
      'hospital': _selectedHospital,
      'type': _type.text,
      'amount': double.tryParse(_amount.text) ?? 0.0,
      'remarks': _remarks.text,
    };

    await DatabaseHelper.instance.insertRecord(record);

    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
      content: Text("Record saved successfully!"),
      backgroundColor: Colors.green,
    ));
  }

  @override
  void dispose() {
    _dateController.dispose();
    _type.dispose();
    _amount.dispose();
    _remarks.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Data Entry")),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            TextFormField(
              controller: _dateController,
              readOnly: true,
              onTap: () => _selectDate(context),
              decoration: const InputDecoration(
                labelText: 'Date',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.calendar_today),
              ),
            ),
            const SizedBox(height: 10),
            DropdownButtonFormField<String>(
              value: _selectedHospital,
              decoration: const InputDecoration(
                labelText: 'Select Hospital',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.local_hospital),
              ),
              hint: const Text('Choose Hospital'),
              items: ['Mettupalayam GH', 'SS Kulam', 'Sumathi', 'Sowmiya']
                  .map((option) => DropdownMenuItem(value: option, child: Text(option)))
                  .toList(),
              onChanged: (value) => setState(() => _selectedHospital = value),
            ),
            const SizedBox(height: 10),
            CustomTextfield(lable: 'Type', hint: 'Surgery Name', TextInputType: TextInputType.text, controller: _type, icon: Icons.medical_services),
            const SizedBox(height: 10),
            CustomTextfield(lable: 'Amount', hint: '₹Amount', TextInputType: TextInputType.number, controller: _amount, icon: Icons.attach_money),
            const SizedBox(height: 10),
            CustomTextfield(lable: 'Remarks', hint: 'Remarks', TextInputType: TextInputType.text, controller: _remarks, icon: Icons.comment),
            const SizedBox(height: 20),
            CustomButton(text: "Submit", icon: Icons.check, color: Colors.blue, onPressed: _saveData),
            const SizedBox(height: 10),
            CustomButton(text: "View Report", icon: Icons.report, color: Colors.orange, onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) => ReportScreen()));
            }),
          ],
        ),
      ),
    );
  }
}