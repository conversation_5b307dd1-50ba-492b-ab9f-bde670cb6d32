import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'Mainscreen.dart';

void main(){
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  runApp(Homescreen());

}
class Homescreen extends StatelessWidget {
  const Homescreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        colorScheme: ColorScheme.light(
          primary: Colors.blue,
          secondary: Colors.amber,
          background: Colors.blue.shade100,
          surface: Colors.grey[200]!,
          onPrimary: Colors.white,
          onSecondary: Colors.black,
        ),
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.light(
          primary: Colors.blue,
          secondary: Colors.amber,
          background: Colors.blue.shade100,
          surface: Colors.grey[200]!,
          onPrimary: Colors.white,
          onSecondary: Colors.black,
        ),
      ),
      themeMode: ThemeMode.system,
      home: Mainscreen(),
    );
  }
}
