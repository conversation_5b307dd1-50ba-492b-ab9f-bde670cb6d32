import 'package:flutter/material.dart';
import 'package:swarnadiary/DatabaseHelper.dart';

class ReportScreen extends StatefulWidget {
  @override
  _ReportScreenState createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> {
  List<Map<String, dynamic>> _records = [];

  Future<void> _fetchRecords() async {
    final records = await DatabaseHelper.instance.fetchRecords();
    setState(() {
      _records = records;
    });
  }

  @override
  void initState() {
    super.initState();
    _fetchRecords();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Report")),
      body: _records.isEmpty
          ? const Center(child: Text("No records found"))
          : ListView.builder(
        itemCount: _records.length,
        itemBuilder: (context, index) {
          final record = _records[index];
          return Card(
            margin: const EdgeInsets.all(8),
            child: ListTile(
              title: Text("${record['hospital']} - ${record['type']}"),
              subtitle: Text("Date: ${record['date']}\nAmount: ₹${record['amount']}\nRemarks: ${record['remarks']}"),
            ),
          );
        },
      ),
    );
  }
}