import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class CustomTextfield extends StatelessWidget {
  final String lable,hint;
  final IconData? icon;
  final TextInputType;
  final TextEditingController controller;

  
   CustomTextfield({super.key, required this.lable,
     this.icon, required this.hint,
     required this.TextInputType, required this.controller,
     });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller:controller,
      keyboardType: TextInputType,
      decoration: InputDecoration(
        labelText: lable,
        border: OutlineInputBorder(),
        prefixIcon: icon != null ? Icon(icon) : null,
        hintText: hint,
      ),
    );
  }
}
