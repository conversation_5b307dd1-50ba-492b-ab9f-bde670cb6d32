import 'package:swarnadiary/DatabaseHelper.dart';

class TestDataHelper {
  static Future<void> addSampleData() async {
    final dbHelper = DatabaseHelper.instance;
    
    // Sample hospital records with various dates and amounts
    final sampleRecords = [
      {
        'date': '2024-01-15',
        'hospital': 'Apollo Hospital',
        'type': 'Heart Surgery',
        'amount': 150000.0,
        'remarks': 'Bypass surgery completed successfully',
      },
      {
        'date': '2024-01-20',
        'hospital': 'Fortis Hospital',
        'type': 'Knee Replacement',
        'amount': 85000.0,
        'remarks': 'Left knee replacement',
      },
      {
        'date': '2024-02-05',
        'hospital': 'Apollo Hospital',
        'type': 'Cataract Surgery',
        'amount': 25000.0,
        'remarks': 'Both eyes operated',
      },
      {
        'date': '2024-02-12',
        'hospital': 'Max Hospital',
        'type': 'Dental Implant',
        'amount': 45000.0,
        'remarks': 'Two implants placed',
      },
      {
        'date': '2024-02-28',
        'hospital': 'Fortis Hospital',
        'type': 'General Checkup',
        'amount': 5000.0,
        'remarks': 'Annual health checkup',
      },
      {
        'date': '2024-03-10',
        'hospital': 'AIIMS',
        'type': 'Brain Surgery',
        'amount': 200000.0,
        'remarks': 'Tumor removal surgery',
      },
      {
        'date': '2024-03-15',
        'hospital': 'Apollo Hospital',
        'type': 'Physiotherapy',
        'amount': 8000.0,
        'remarks': '10 sessions completed',
      },
      {
        'date': '2024-03-22',
        'hospital': 'Max Hospital',
        'type': 'Blood Test',
        'amount': 2500.0,
        'remarks': 'Complete blood count and lipid profile',
      },
      {
        'date': '2024-04-01',
        'hospital': 'Fortis Hospital',
        'type': 'Ultrasound',
        'amount': 3500.0,
        'remarks': 'Abdominal ultrasound',
      },
      {
        'date': '2024-04-08',
        'hospital': 'AIIMS',
        'type': 'Consultation',
        'amount': 1500.0,
        'remarks': 'Follow-up consultation',
      },
      {
        'date': '2024-04-15',
        'hospital': 'Apollo Hospital',
        'type': 'MRI Scan',
        'amount': 12000.0,
        'remarks': 'Brain MRI with contrast',
      },
      {
        'date': '2024-05-02',
        'hospital': 'Max Hospital',
        'type': 'Surgery',
        'amount': 75000.0,
        'remarks': 'Gallbladder removal',
      },
      {
        'date': '2024-05-10',
        'hospital': 'Fortis Hospital',
        'type': 'X-Ray',
        'amount': 1200.0,
        'remarks': 'Chest X-ray',
      },
      {
        'date': '2024-05-18',
        'hospital': 'Apollo Hospital',
        'type': 'ECG',
        'amount': 800.0,
        'remarks': 'Routine ECG',
      },
      {
        'date': '2024-06-01',
        'hospital': 'AIIMS',
        'type': 'Chemotherapy',
        'amount': 95000.0,
        'remarks': 'First cycle of chemotherapy',
      },
    ];

    // Insert all sample records
    for (final record in sampleRecords) {
      await dbHelper.insertRecord(record);
    }
    
    print('Sample data added successfully!');
  }
  
  static Future<void> clearAllData() async {
    final dbHelper = DatabaseHelper.instance;
    final db = await dbHelper.database;
    await db.delete('records');
    print('All data cleared!');
  }
}
