import 'package:flutter_test/flutter_test.dart';
import 'package:swarnadiary/DatabaseHelper.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  setUpAll(() {
    // Initialize FFI
    sqfliteFfiInit();
    // Change the default factory
    databaseFactory = databaseFactoryFfi;
  });

  group('Advanced Reports Tests', () {
    late DatabaseHelper dbHelper;

    setUp(() async {
      dbHelper = DatabaseHelper.instance;
      // Clear any existing data
      final db = await dbHelper.database;
      await db.delete('records');
    });

    test('should insert and fetch records correctly', () async {
      final record = {
        'date': '2024-01-15',
        'hospital': 'Test Hospital',
        'type': 'Test Surgery',
        'amount': 50000.0,
        'remarks': 'Test remarks',
      };

      await dbHelper.insertRecord(record);
      final records = await dbHelper.fetchRecords();

      expect(records.length, 1);
      expect(records.first['hospital'], 'Test Hospital');
      expect(records.first['amount'], 50000.0);
    });

    test('should calculate monthly totals correctly', () async {
      // Add test records for different months
      await dbHelper.insertRecord({
        'date': '2024-01-15',
        'hospital': 'Hospital A',
        'type': 'Surgery',
        'amount': 30000.0,
        'remarks': 'Test',
      });

      await dbHelper.insertRecord({
        'date': '2024-01-20',
        'hospital': 'Hospital B',
        'type': 'Checkup',
        'amount': 20000.0,
        'remarks': 'Test',
      });

      await dbHelper.insertRecord({
        'date': '2024-02-10',
        'hospital': 'Hospital A',
        'type': 'Surgery',
        'amount': 40000.0,
        'remarks': 'Test',
      });

      final monthlyTotals = await dbHelper.getMonthlyTotals();

      expect(monthlyTotals.length, 2);
      
      // Find January and February totals
      final jan2024 = monthlyTotals.firstWhere((m) => m['month'] == '2024-01');
      final feb2024 = monthlyTotals.firstWhere((m) => m['month'] == '2024-02');

      expect(jan2024['total_amount'], 50000.0);
      expect(jan2024['record_count'], 2);
      expect(feb2024['total_amount'], 40000.0);
      expect(feb2024['record_count'], 1);
    });

    test('should calculate hospital-wise totals correctly', () async {
      await dbHelper.insertRecord({
        'date': '2024-01-15',
        'hospital': 'Apollo Hospital',
        'type': 'Surgery',
        'amount': 60000.0,
        'remarks': 'Test',
      });

      await dbHelper.insertRecord({
        'date': '2024-01-20',
        'hospital': 'Apollo Hospital',
        'type': 'Checkup',
        'amount': 40000.0,
        'remarks': 'Test',
      });

      await dbHelper.insertRecord({
        'date': '2024-02-10',
        'hospital': 'Fortis Hospital',
        'type': 'Surgery',
        'amount': 30000.0,
        'remarks': 'Test',
      });

      final hospitalTotals = await dbHelper.getHospitalWiseTotals();

      expect(hospitalTotals.length, 2);
      
      final apollo = hospitalTotals.firstWhere((h) => h['hospital'] == 'Apollo Hospital');
      final fortis = hospitalTotals.firstWhere((h) => h['hospital'] == 'Fortis Hospital');

      expect(apollo['total_amount'], 100000.0);
      expect(apollo['record_count'], 2);
      expect(apollo['avg_amount'], 50000.0);
      
      expect(fortis['total_amount'], 30000.0);
      expect(fortis['record_count'], 1);
      expect(fortis['avg_amount'], 30000.0);
    });

    test('should calculate overall stats correctly', () async {
      await dbHelper.insertRecord({
        'date': '2024-01-15',
        'hospital': 'Hospital A',
        'type': 'Surgery',
        'amount': 10000.0,
        'remarks': 'Test',
      });

      await dbHelper.insertRecord({
        'date': '2024-01-20',
        'hospital': 'Hospital B',
        'type': 'Checkup',
        'amount': 30000.0,
        'remarks': 'Test',
      });

      await dbHelper.insertRecord({
        'date': '2024-02-10',
        'hospital': 'Hospital C',
        'type': 'Surgery',
        'amount': 20000.0,
        'remarks': 'Test',
      });

      final stats = await dbHelper.getOverallStats();

      expect(stats['total_records'], 3);
      expect(stats['total_amount'], 60000.0);
      expect(stats['avg_amount'], 20000.0);
      expect(stats['min_amount'], 10000.0);
      expect(stats['max_amount'], 30000.0);
      expect(stats['earliest_date'], '2024-01-15');
      expect(stats['latest_date'], '2024-02-10');
    });
  });
}
